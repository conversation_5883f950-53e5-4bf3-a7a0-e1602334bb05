<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beautiful Login - 选择您喜欢的风格</title>
    <link rel="stylesheet" href="./assets/styles/style-selector.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">🎨 Beautiful Login</h1>
            <p class="subtitle">选择您喜欢的登录页面风格</p>
        </header>

        <div class="styles-grid">
            <!-- 现代渐变风格 -->
            <div class="style-card" data-style="modern-gradient">
                <div class="card-preview modern-gradient-preview">
                    <div class="preview-bg">
                        <div class="floating-bubble"></div>
                        <div class="floating-bubble"></div>
                        <div class="floating-bubble"></div>
                    </div>
                    <div class="preview-form">
                        <div class="form-title">登录</div>
                        <div class="form-input"></div>
                        <div class="form-input"></div>
                        <div class="form-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🌈 现代渐变风格</h3>
                    <p>科技感十足的渐变背景配合浮动动画</p>
                    <ul class="features">
                        <li>青蓝到紫色渐变背景</li>
                        <li>浮动气泡动画效果</li>
                        <li>玻璃拟态设计风格</li>
                        <li>适用：科技、创新类应用</li>
                    </ul>
                </div>
            </div>

            <!-- 深色极简风格 -->
            <div class="style-card" data-style="dark-minimal">
                <div class="card-preview dark-minimal-preview">
                    <div class="preview-bg">
                        <div class="grid-pattern"></div>
                    </div>
                    <div class="preview-form dark-form">
                        <div class="form-title">登录</div>
                        <div class="form-input neon-border"></div>
                        <div class="form-input neon-border"></div>
                        <div class="form-button neon-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🌙 深色极简风格</h3>
                    <p>深色主题配合霓虹边框效果</p>
                    <ul class="features">
                        <li>深灰/黑色背景</li>
                        <li>蓝绿霓虹色边框</li>
                        <li>极简主义设计</li>
                        <li>适用：游戏、娱乐类应用</li>
                    </ul>
                </div>
            </div>

            <!-- 温馨粉色风格 -->
            <div class="style-card" data-style="warm-pink">
                <div class="card-preview warm-pink-preview">
                    <div class="preview-bg">
                        <div class="petal"></div>
                        <div class="petal"></div>
                        <div class="petal"></div>
                    </div>
                    <div class="preview-form pink-form">
                        <div class="form-title">登录</div>
                        <div class="form-input"></div>
                        <div class="form-input"></div>
                        <div class="form-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🌸 温馨粉色风格</h3>
                    <p>柔和粉色配合花瓣飘落动画</p>
                    <ul class="features">
                        <li>粉色到橙色温暖渐变</li>
                        <li>花瓣飘落动画效果</li>
                        <li>温馨可爱的设计风格</li>
                        <li>适用：社交、生活类应用</li>
                    </ul>
                </div>
            </div>

            <!-- 商务专业风格 -->
            <div class="style-card" data-style="business-professional">
                <div class="card-preview business-preview">
                    <div class="preview-bg">
                        <div class="geometric-shape"></div>
                        <div class="geometric-shape"></div>
                    </div>
                    <div class="preview-form business-form">
                        <div class="form-title">登录</div>
                        <div class="form-input"></div>
                        <div class="form-input"></div>
                        <div class="form-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🏢 商务专业风格</h3>
                    <p>简洁白色配合几何图形装饰</p>
                    <ul class="features">
                        <li>简洁白色背景</li>
                        <li>蓝色商务配色</li>
                        <li>几何图形装饰元素</li>
                        <li>适用：企业、金融类应用</li>
                    </ul>
                </div>
            </div>

            <!-- 海洋蓝调风格 -->
            <div class="style-card" data-style="ocean-blue">
                <div class="card-preview ocean-blue-preview">
                    <div class="preview-bg">
                        <div class="wave-animation"></div>
                        <div class="bubble-float"></div>
                        <div class="bubble-float"></div>
                    </div>
                    <div class="preview-form ocean-form">
                        <div class="form-title">登录</div>
                        <div class="form-input"></div>
                        <div class="form-input"></div>
                        <div class="form-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🌊 海洋蓝调风格</h3>
                    <p>海洋波浪动画配合水滴效果</p>
                    <ul class="features">
                        <li>深蓝到浅蓝渐变</li>
                        <li>海洋波浪动画</li>
                        <li>水滴飘落效果</li>
                        <li>适用：旅游、海洋类应用</li>
                    </ul>
                </div>
            </div>

            <!-- 星空科幻风格 -->
            <div class="style-card" data-style="starry-scifi">
                <div class="card-preview starry-scifi-preview">
                    <div class="preview-bg">
                        <div class="star-twinkle"></div>
                        <div class="star-twinkle"></div>
                        <div class="star-twinkle"></div>
                        <div class="meteor-trail"></div>
                    </div>
                    <div class="preview-form scifi-form">
                        <div class="form-title">登录</div>
                        <div class="form-input"></div>
                        <div class="form-input"></div>
                        <div class="form-button"></div>
                    </div>
                </div>
                <div class="card-info">
                    <h3>🌟 星空科幻风格</h3>
                    <p>星空背景配合粒子系统</p>
                    <ul class="features">
                        <li>深紫到黑色背景</li>
                        <li>金色星光效果</li>
                        <li>流星粒子动画</li>
                        <li>适用：科技、游戏类应用</li>
                    </ul>
                </div>
            </div>
        </div>

        <footer class="footer">
            <p>© 2024 Beautiful Login Project. 精美移动端登录页面集合 - 现已支持6种风格</p>
        </footer>
    </div>

    <script src="js/style-selector.js"></script>
</body>
</html>
