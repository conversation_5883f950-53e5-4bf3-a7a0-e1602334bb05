* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 300;
}

.styles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.style-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    cursor: pointer;
}

.style-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.card-preview {
    height: 200px;
    position: relative;
    overflow: hidden;
}

/* 现代渐变风格预览 */
.modern-gradient-preview {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.floating-bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 4s ease-in-out infinite;
}

.floating-bubble:nth-child(1) {
    width: 30px;
    height: 30px;
    left: 20%;
    top: 20%;
    animation-delay: 0s;
}

.floating-bubble:nth-child(2) {
    width: 20px;
    height: 20px;
    right: 30%;
    top: 60%;
    animation-delay: 1s;
}

.floating-bubble:nth-child(3) {
    width: 25px;
    height: 25px;
    left: 60%;
    top: 30%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 深色极简风格预览 */
.dark-minimal-preview {
    background: #1a1a1a;
}

.grid-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: gridMove 10s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

/* 温馨粉色风格预览 */
.warm-pink-preview {
    background: linear-gradient(45deg, #ff9a9e, #fecfef);
}

.petal {
    position: absolute;
    width: 10px;
    height: 10px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50% 0;
    animation: petalFall 6s linear infinite;
}

.petal:nth-child(1) {
    left: 20%;
    animation-delay: 0s;
}

.petal:nth-child(2) {
    left: 50%;
    animation-delay: 2s;
}

.petal:nth-child(3) {
    left: 80%;
    animation-delay: 4s;
}

@keyframes petalFall {
    0% {
        transform: translateY(-20px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(220px) rotate(360deg);
        opacity: 0;
    }
}

/* 商务专业风格预览 */
.business-preview {
    background: #f8f9fa;
}

.geometric-shape {
    position: absolute;
    border: 2px solid #007bff;
    opacity: 0.3;
}

.geometric-shape:nth-child(1) {
    width: 40px;
    height: 40px;
    right: 20px;
    top: 20px;
    transform: rotate(45deg);
}

.geometric-shape:nth-child(2) {
    width: 30px;
    height: 30px;
    left: 30px;
    bottom: 30px;
    border-radius: 50%;
}

/* 预览表单样式 */
.preview-form {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
}

.form-title {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
    color: white;
}

.dark-form .form-title {
    color: #00ffff;
}

.pink-form .form-title,
.business-form .form-title {
    color: #333;
}

.form-input {
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    margin-bottom: 6px;
}

.neon-border {
    background: rgba(0, 255, 255, 0.2);
    border: 1px solid #00ffff;
}

.form-button {
    height: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 5px;
    margin-top: 8px;
}

.neon-button {
    background: #00ffff;
}

/* 卡片信息区域 */
.card-info {
    padding: 20px;
}

.card-info h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: #333;
}

.card-info p {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.features {
    list-style: none;
}

.features li {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 5px;
    padding-left: 15px;
    position: relative;
}

.features li:before {
    content: "•";
    color: #007bff;
    position: absolute;
    left: 0;
}

/* 海洋蓝调风格预览 */
.ocean-blue-preview {
    background: linear-gradient(180deg, #87CEEB 0%, #4682B4 50%, #191970 100%);
}

.wave-animation {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50% 50% 0 0;
    animation: waveMove 3s ease-in-out infinite;
}

@keyframes waveMove {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(-10px); }
}

.bubble-float {
    position: absolute;
    width: 8px;
    height: 8px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    animation: bubbleFloat 4s ease-in-out infinite;
}

.bubble-float:nth-child(2) {
    left: 20%;
    bottom: 30%;
    animation-delay: 0s;
}

.bubble-float:nth-child(3) {
    right: 20%;
    bottom: 50%;
    animation-delay: 1s;
}

@keyframes bubbleFloat {
    0%, 100% { transform: translateY(0); opacity: 0.4; }
    50% { transform: translateY(-10px); opacity: 0.8; }
}

.ocean-form {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 119, 190, 0.3);
}

.ocean-form .form-title {
    color: #0077be;
}

.ocean-form .form-input {
    border-bottom: 2px solid rgba(0, 119, 190, 0.3);
}

.ocean-form .form-button {
    background: linear-gradient(135deg, #87CEEB, #0077be);
}

/* 星空科幻风格预览 */
.starry-scifi-preview {
    background: radial-gradient(ellipse at bottom, #1B2735 0%, #090A0F 100%);
}

.star-twinkle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #FFD700;
    border-radius: 50%;
    animation: starTwinkle 2s ease-in-out infinite;
}

.star-twinkle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.star-twinkle:nth-child(2) {
    top: 40%;
    right: 30%;
    animation-delay: 0.7s;
}

.star-twinkle:nth-child(3) {
    bottom: 30%;
    left: 60%;
    animation-delay: 1.4s;
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.meteor-trail {
    position: absolute;
    top: 10%;
    right: 10%;
    width: 2px;
    height: 2px;
    background: #FFD700;
    border-radius: 50%;
    animation: meteorFall 3s linear infinite;
}

@keyframes meteorFall {
    0% {
        transform: translateX(0) translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateX(-30px) translateY(30px);
        opacity: 0;
    }
}

.scifi-form {
    background: rgba(20, 20, 30, 0.9);
    border: 2px solid #FFD700;
}

.scifi-form .form-title {
    color: #FFD700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.scifi-form .form-input {
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

.scifi-form .form-button {
    background: rgba(255, 215, 0, 0.1);
    border: 2px solid #FFD700;
    color: #FFD700;
}

.footer {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .styles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .style-card {
        margin: 0 10px;
    }
}
