* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
}

/* 返回按钮 */
.back-button {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 动态背景 */
.background {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 0;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
    backdrop-filter: blur(5px);
}

.bubble:nth-child(1) {
    width: 80px;
    height: 80px;
    left: 10%;
    top: 20%;
    animation-delay: 0s;
}

.bubble:nth-child(2) {
    width: 100px;
    height: 100px;
    right: 15%;
    top: 10%;
    animation-delay: 2s;
}

.bubble:nth-child(3) {
    width: 60px;
    height: 60px;
    left: 70%;
    top: 60%;
    animation-delay: 4s;
}

.bubble:nth-child(4) {
    width: 120px;
    height: 120px;
    left: 20%;
    bottom: 20%;
    animation-delay: 1s;
}

.bubble:nth-child(5) {
    width: 90px;
    height: 90px;
    right: 25%;
    bottom: 30%;
    animation-delay: 3s;
}

.bubble:nth-child(6) {
    width: 70px;
    height: 70px;
    left: 50%;
    top: 80%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
        opacity: 1;
    }
}

/* 玻璃卡片容器 */
.login-container {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 400px;
    margin: 0 20px;
}

.glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.glass-card h2 {
    text-align: center;
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.subtitle {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 30px;
    font-weight: 300;
}

/* 表单样式 */
.form-group {
    position: relative;
    margin-bottom: 25px;
}

.form-group input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.form-group input:focus {
    border-bottom-color: rgba(255, 255, 255, 0.8);
}

.form-group input::placeholder {
    color: transparent;
}

.form-group label {
    position: absolute;
    left: 0;
    top: 15px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input:focus + label,
.form-group input:valid + label {
    top: -5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
}

.input-highlight {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.form-group input:focus ~ .input-highlight {
    width: 100%;
}

/* 验证码组 */
.verification-group {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.verification-group input {
    flex: 1;
}

.verify-btn {
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.verify-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.verify-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 20px 0;
}

.login-btn:hover {
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3));
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.login-btn span {
    position: relative;
    z-index: 2;
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.login-btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

/* 附加链接 */
.additional-links {
    text-align: center;
    margin-top: 20px;
}

.additional-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.additional-links a:hover {
    color: white;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.divider {
    color: rgba(255, 255, 255, 0.5);
    margin: 0 15px;
}

/* 隐藏和显示动画 */
.hidden {
    display: none;
}

.fade-out {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .glass-card {
        padding: 30px 20px;
        margin: 0 15px;
    }
    
    .back-button {
        top: 15px;
        left: 15px;
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .verification-group {
        flex-direction: column;
        gap: 15px;
    }
    
    .verify-btn {
        width: 100%;
    }
}
