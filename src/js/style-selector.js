// 风格选择页面的JavaScript功能
document.addEventListener('DOMContentLoaded', () => {
    // 初始化页面动画
    initPageAnimations();
    
    // 绑定风格卡片点击事件
    bindStyleCardEvents();
    
    // 初始化交互效果
    initInteractiveEffects();
});

function initPageAnimations() {
    // 标题入场动画
    const title = document.querySelector('.title');
    if (title) {
        title.style.opacity = '0';
        title.style.transform = 'translateY(-30px)';
        
        setTimeout(() => {
            title.style.transition = 'all 0.8s ease';
            title.style.opacity = '1';
            title.style.transform = 'translateY(0)';
        }, 200);
    }

    // 副标题入场动画
    const subtitle = document.querySelector('.subtitle');
    if (subtitle) {
        subtitle.style.opacity = '0';
        subtitle.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            subtitle.style.transition = 'all 0.8s ease';
            subtitle.style.opacity = '1';
            subtitle.style.transform = 'translateY(0)';
        }, 400);
    }

    // 风格卡片依次入场动画
    const styleCards = document.querySelectorAll('.style-card');
    styleCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px) scale(0.9)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
        }, 600 + index * 100);
    });
}

function bindStyleCardEvents() {
    const styleCards = document.querySelectorAll('.style-card');
    const styleUrls = {
        'modern-gradient': 'styles/modern-gradient.html',
        'dark-minimal': 'styles/dark-minimal.html',
        'warm-pink': 'styles/warm-pink.html',
        'business-professional': 'styles/business-professional.html',
        'ocean-blue': 'styles/ocean-blue.html',
        'starry-scifi': 'styles/starry-scifi.html'
    };

    styleCards.forEach(card => {
        card.addEventListener('click', () => {
            const style = card.dataset.style;
            const url = styleUrls[style];
            
            if (url) {
                // 添加点击动画
                card.style.transform = 'scale(0.95)';
                
                setTimeout(() => {
                    // 页面跳转
                    window.location.href = url;
                }, 150);
            }
        });

        // 悬停效果
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
            card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.2)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
        });
    });
}

function initInteractiveEffects() {
    // 鼠标移动时的背景响应
    document.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;
        
        updateBackgroundGradient(mouseX, mouseY);
        updateFloatingElements(mouseX, mouseY);
    });

    // 滚动时的视差效果
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * 0.5;
        
        const background = document.querySelector('.background');
        if (background) {
            background.style.transform = `translateY(${parallax}px)`;
        }
    });
}

function updateBackgroundGradient(mouseX, mouseY) {
    const background = document.querySelector('.background');
    if (background) {
        const hue1 = 220 + (mouseX * 40);
        const hue2 = 280 + (mouseY * 40);
        
        background.style.background = `linear-gradient(135deg, 
            hsl(${hue1}, 70%, 60%) 0%, 
            hsl(${hue2}, 60%, 50%) 100%)`;
    }
}

function updateFloatingElements(mouseX, mouseY) {
    // 更新浮动气泡位置
    const bubbles = document.querySelectorAll('.floating-bubble');
    bubbles.forEach((bubble, index) => {
        const factor = (index + 1) * 0.1;
        const offsetX = (mouseX - 0.5) * 30 * factor;
        const offsetY = (mouseY - 0.5) * 20 * factor;
        
        bubble.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
    });

    // 更新花瓣位置
    const petals = document.querySelectorAll('.petal');
    petals.forEach((petal, index) => {
        const factor = (index + 1) * 0.15;
        const offsetX = (mouseX - 0.5) * 25 * factor;
        const offsetY = (mouseY - 0.5) * 15 * factor;
        
        petal.style.transform = `translate(${offsetX}px, ${offsetY}px) rotate(${offsetX}deg)`;
    });

    // 更新几何图形位置
    const shapes = document.querySelectorAll('.geometric-shape');
    shapes.forEach((shape, index) => {
        const factor = (index + 1) * 0.2;
        const offsetX = (mouseX - 0.5) * 20 * factor;
        const offsetY = (mouseY - 0.5) * 10 * factor;
        
        shape.style.transform = `translate(${offsetX}px, ${offsetY}px) rotate(${offsetX * 2}deg)`;
    });

    // 更新海洋元素
    const waves = document.querySelectorAll('.wave-animation');
    waves.forEach(wave => {
        const intensity = mouseX * mouseY;
        wave.style.animationDuration = (3 - intensity * 1) + 's';
    });

    // 更新星空元素
    const stars = document.querySelectorAll('.star-twinkle');
    stars.forEach((star, index) => {
        const factor = (index + 1) * 0.1;
        const intensity = mouseX * mouseY * factor;
        star.style.opacity = 0.3 + intensity * 0.7;
        star.style.transform = `scale(${1 + intensity * 0.5})`;
    });
}

// 添加键盘导航支持
document.addEventListener('keydown', (e) => {
    const styleCards = document.querySelectorAll('.style-card');
    const currentFocus = document.activeElement;
    const currentIndex = Array.from(styleCards).indexOf(currentFocus);

    switch(e.key) {
        case 'ArrowRight':
        case 'ArrowDown':
            e.preventDefault();
            const nextIndex = (currentIndex + 1) % styleCards.length;
            styleCards[nextIndex].focus();
            break;
            
        case 'ArrowLeft':
        case 'ArrowUp':
            e.preventDefault();
            const prevIndex = currentIndex === -1 ? 0 : (currentIndex - 1 + styleCards.length) % styleCards.length;
            styleCards[prevIndex].focus();
            break;
            
        case 'Enter':
        case ' ':
            if (currentFocus && currentFocus.classList.contains('style-card')) {
                e.preventDefault();
                currentFocus.click();
            }
            break;
    }
});

// 使风格卡片可聚焦
document.querySelectorAll('.style-card').forEach(card => {
    card.setAttribute('tabindex', '0');
    
    // 聚焦时的视觉反馈
    card.addEventListener('focus', () => {
        card.style.outline = '3px solid rgba(255, 255, 255, 0.5)';
        card.style.outlineOffset = '4px';
    });
    
    card.addEventListener('blur', () => {
        card.style.outline = 'none';
    });
});

// 页面加载完成提示
window.addEventListener('load', () => {
    console.log('🎨 Beautiful Login - 风格选择页面加载完成');
    console.log('📱 支持的风格：现代渐变、深色极简、温馨粉色、商务专业、海洋蓝调、星空科幻');
});
