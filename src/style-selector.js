document.addEventListener('DOMContentLoaded', () => {
    const styleCards = document.querySelectorAll('.style-card');
    
    // 为每个风格卡片添加点击事件
    styleCards.forEach(card => {
        card.addEventListener('click', () => {
            const style = card.dataset.style;
            
            // 添加点击动画效果
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
            
            // 延迟跳转，让动画完成
            setTimeout(() => {
                window.location.href = `styles/${style}.html`;
            }, 200);
        });
        
        // 添加悬停效果
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
        });
    });
    
    // 添加页面加载动画
    const cards = document.querySelectorAll('.style-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
    
    // 标题动画
    const title = document.querySelector('.title');
    const subtitle = document.querySelector('.subtitle');
    
    title.style.opacity = '0';
    title.style.transform = 'translateY(-30px)';
    subtitle.style.opacity = '0';
    subtitle.style.transform = 'translateY(-20px)';
    
    setTimeout(() => {
        title.style.transition = 'all 0.8s ease';
        title.style.opacity = '1';
        title.style.transform = 'translateY(0)';
    }, 100);
    
    setTimeout(() => {
        subtitle.style.transition = 'all 0.8s ease';
        subtitle.style.opacity = '1';
        subtitle.style.transform = 'translateY(0)';
    }, 300);
});
